<?php

namespace App\Models;

use Altek\Accountant\Contracts\Recordable;
use Altek\Accountant\Recordable as RecordsToLedger;
use Altek\Eventually\Eventually as RecordsPivotsToLedger;
use App\Enums\AgentCtaResponseBasis;
use App\Enums\AgentCtaTimingMode;
use App\Enums\AgentCtaDisplayMode;
use App\Models\Traits\SyncsToFrontend;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Spatie\Translatable\HasTranslations;

class AgentCta extends Model implements Recordable
{
    use HasFactory;
    use HasTranslations;
    use RecordsPivotsToLedger;
    use RecordsToLedger;
    use SoftDeletes;
    use SyncsToFrontend;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'agent_id',
        'is_active',
        'standalone_active',
        'embedded_active',
        'api_active',
        'display_mode',
        'content',
        'timing_mode',
        'response_basis',
        'response_number',
        'priority',
    ];

    /**
     * @var string[]
     */
    public $translatable = [
        'content',
    ];

    /**
     * API
     *
     * @var array|string[]
     */
    public static array $allowedIncludes = [
        'agent',
    ];

    public static string $defaultSort = 'name';

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'is_active' => 'boolean',
            'standalone_active' => 'boolean',
            'embedded_active' => 'boolean',
            'api_active' => 'boolean',
            'display_mode' => AgentCtaDisplayMode::class,
            'content' => 'json',
            'timing_mode' => AgentCtaTimingMode::class,
            'response_basis' => AgentCtaResponseBasis::class,
            'response_number' => 'integer',
            'priority' => 'integer',
        ];
    }

    /**
     * The model's default attribute values.
     *
     * @var array<string, mixed>
     */
    protected $attributes = [
        'content' => '{}',
    ];

    public function agent(): BelongsTo
    {
        return $this->belongsTo(Agent::class);
    }

    public function integrations(): BelongsToMany
    {

        $relation = $this->belongsToMany(
            AgentIntegration::class,
            'agent_integration_cta',
            'cta_id',
            'integration_id',
        );

        if ($this->agent_id) {
            $relation->where('agent_id', $this->agent_id);
        }

        return $relation;

    }

    public function syncToFrontend(): void
    {

        increase_timeout('HIGH');

        $this->pushFieldsToFrontend();
        $this->pruneDeletedFromFrontend();

        DB::connection(env('FRONTEND_DB_CONNECTION'))
            ->table($this->getTable())
            ->where('id', $this->id)
            ->update([
                'integrations' => $this->integrations()->pluck('agent_integrations.id'),
            ])
        ;

    }

}
